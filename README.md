# AI Resume Optimizer

An intelligent resume optimization tool that helps job seekers tailor their resumes to specific job postings using AI. Built with Next.js and OpenAI.

## Features

- **Resume Analysis & Optimization**: Upload your resume (PDF, DOC, DOCX, or TXT) and get AI-powered suggestions to improve it
- **Job Search Integration**: Search for relevant job postings directly within the application
- **ATS Optimization**: Ensures your resume is optimized for Applicant Tracking Systems (ATS)
- **Cover Letter Generation**: Automatically generate tailored cover letters based on your resume and job descriptions
- **Modern UI**: Beautiful and responsive interface built with Tailwind CSS and Framer Motion

## Tech Stack

- [Next.js](https://nextjs.org) - React framework for production
- [OpenAI API](https://openai.com) - AI-powered resume analysis and optimization
- [Tailwind CSS](https://tailwindcss.com) - Utility-first CSS framework
- [Framer Motion](https://www.framer.com/motion/) - Animation library
- [TypeScript](https://www.typescriptlang.org/) - Type-safe JavaScript

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```
3. Set up your environment variables:
   ```
   OPENAI_API_KEY=your_openai_api_key
   ```

4. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) with your browser to see the application

## How It Works

1. **Upload Your Resume**: Support for PDF, DOC, DOCX, and TXT formats
2. **Find Job Postings**: Search for relevant job postings or paste a job description
3. **Get Optimization Suggestions**: Receive AI-powered suggestions to improve your resume
4. **Generate Cover Letter**: Automatically create a tailored cover letter for the position

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is open source and available under the [MIT License](LICENSE).
